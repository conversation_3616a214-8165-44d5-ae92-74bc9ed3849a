<svg viewBox="0 0 1000 700" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151" />
    </marker>
  </defs>
  
  <!-- Background -->
  <rect width="1000" height="700" fill="#f8fafc"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1000" height="60" fill="url(#headerGradient)"/>
  <text x="500" y="35" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="bold">智能合同生成流程时序图</text>
  <text x="500" y="50" text-anchor="middle" fill="#e2e8f0" font-family="Arial, sans-serif" font-size="12">AI驱动的合同生成交互流程</text>
  
  <!-- Participants -->
  <g id="participants">
    <!-- User -->
    <rect x="80" y="80" width="100" height="40" fill="#059669" rx="8" filter="url(#shadow)"/>
    <text x="130" y="105" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">用户</text>
    
    <!-- AI Service -->
    <rect x="250" y="80" width="100" height="40" fill="#dc2626" rx="8" filter="url(#shadow)"/>
    <text x="300" y="105" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">AI服务</text>
    
    <!-- Knowledge Base -->
    <rect x="420" y="80" width="100" height="40" fill="#7c3aed" rx="8" filter="url(#shadow)"/>
    <text x="470" y="105" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">知识库</text>
    
    <!-- Template Library -->
    <rect x="590" y="80" width="100" height="40" fill="#ea580c" rx="8" filter="url(#shadow)"/>
    <text x="640" y="105" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">模板库</text>
  </g>
  
  <!-- Lifelines -->
  <g id="lifelines" stroke="#cbd5e1" stroke-width="2" stroke-dasharray="5,5">
    <line x1="130" y1="120" x2="130" y2="620"/>
    <line x1="300" y1="120" x2="300" y2="620"/>
    <line x1="470" y1="120" x2="470" y2="620"/>
    <line x1="640" y1="120" x2="640" y2="620"/>
  </g>
  
  <!-- Messages -->
  <g id="messages">
    <!-- Message 1: User -> AI -->
    <line x1="130" y1="160" x2="300" y2="160" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
    <rect x="140" y="145" width="150" height="30" fill="#ffffff" stroke="#e5e7eb" rx="4" filter="url(#shadow)"/>
    <text x="215" y="160" text-anchor="middle" fill="#374151" font-family="Arial, sans-serif" font-size="11" font-weight="bold">1. 发起合同生成请求</text>
    
    <!-- Message 2: AI -> User -->
    <line x1="300" y1="200" x2="130" y2="200" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
    <rect x="140" y="185" width="150" height="30" fill="#fef2f2" stroke="#fca5a5" rx="4" filter="url(#shadow)"/>
    <text x="215" y="200" text-anchor="middle" fill="#dc2626" font-family="Arial, sans-serif" font-size="11" font-weight="bold">2. 询问合同基本信息</text>
    
    <!-- Message 3: User -> AI -->
    <line x1="130" y1="240" x2="300" y2="240" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
    <rect x="140" y="225" width="150" height="30" fill="#ffffff" stroke="#e5e7eb" rx="4" filter="url(#shadow)"/>
    <text x="215" y="240" text-anchor="middle" fill="#374151" font-family="Arial, sans-serif" font-size="11" font-weight="bold">3. 描述合同需求</text>
    
    <!-- Message 4: AI -> KB -->
    <line x1="300" y1="280" x2="470" y2="280" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
    <rect x="310" y="265" width="150" height="30" fill="#faf5ff" stroke="#c4b5fd" rx="4" filter="url(#shadow)"/>
    <text x="385" y="280" text-anchor="middle" fill="#7c3aed" font-family="Arial, sans-serif" font-size="11" font-weight="bold">4. 检索相关法律条款</text>
    
    <!-- Message 5: AI -> Template -->
    <line x1="300" y1="320" x2="640" y2="320" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
    <rect x="400" y="305" width="140" height="30" fill="#fff7ed" stroke="#fdba74" rx="4" filter="url(#shadow)"/>
    <text x="470" y="320" text-anchor="middle" fill="#ea580c" font-family="Arial, sans-serif" font-size="11" font-weight="bold">5. 匹配相似模板</text>
    
    <!-- Message 6: AI -> User -->
    <line x1="300" y1="360" x2="130" y2="360" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
    <rect x="140" y="345" width="150" height="30" fill="#fef2f2" stroke="#fca5a5" rx="4" filter="url(#shadow)"/>
    <text x="215" y="360" text-anchor="middle" fill="#dc2626" font-family="Arial, sans-serif" font-size="11" font-weight="bold">6. 返回生成的合同初稿</text>
    
    <!-- Message 7: User -> AI -->
    <line x1="130" y1="400" x2="300" y2="400" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
    <rect x="140" y="385" width="150" height="30" fill="#ffffff" stroke="#e5e7eb" rx="4" filter="url(#shadow)"/>
    <text x="215" y="400" text-anchor="middle" fill="#374151" font-family="Arial, sans-serif" font-size="11" font-weight="bold">7. 请求修改优化</text>
    
    <!-- Message 8: AI -> User -->
    <line x1="300" y1="440" x2="130" y2="440" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
    <rect x="140" y="425" width="150" height="30" fill="#fef2f2" stroke="#fca5a5" rx="4" filter="url(#shadow)"/>
    <text x="215" y="440" text-anchor="middle" fill="#dc2626" font-family="Arial, sans-serif" font-size="11" font-weight="bold">8. 提供最终合同版本</text>
  </g>
  
  <!-- Activation boxes -->
  <g id="activations">
    <!-- User activations -->
    <rect x="125" y="155" width="10" height="15" fill="#059669" rx="2"/>
    <rect x="125" y="235" width="10" height="15" fill="#059669" rx="2"/>
    <rect x="125" y="395" width="10" height="15" fill="#059669" rx="2"/>
    
    <!-- AI activations -->
    <rect x="295" y="155" width="10" height="295" fill="#dc2626" rx="2"/>
    
    <!-- KB activation -->
    <rect x="465" y="275" width="10" height="15" fill="#7c3aed" rx="2"/>
    
    <!-- Template activation -->
    <rect x="635" y="315" width="10" height="15" fill="#ea580c" rx="2"/>
  </g>
  
  <!-- Phase labels -->
  <g id="phases">
    <rect x="750" y="140" width="200" height="100" fill="#f1f5f9" stroke="#cbd5e1" rx="6" filter="url(#shadow)"/>
    <text x="850" y="160" text-anchor="middle" fill="#1e293b" font-family="Arial, sans-serif" font-size="14" font-weight="bold">🔄 交互阶段</text>
    <text x="760" y="180" fill="#475569" font-family="Arial, sans-serif" font-size="11">• 需求收集</text>
    <text x="760" y="195" fill="#475569" font-family="Arial, sans-serif" font-size="11">• 信息确认</text>
    <text x="760" y="210" fill="#475569" font-family="Arial, sans-serif" font-size="11">• 初步交流</text>
    <text x="760" y="225" fill="#475569" font-family="Arial, sans-serif" font-size="11">• 需求明确</text>
    
    <rect x="750" y="260" width="200" height="100" fill="#f0fdf4" stroke="#bbf7d0" rx="6" filter="url(#shadow)"/>
    <text x="850" y="280" text-anchor="middle" fill="#166534" font-family="Arial, sans-serif" font-size="14" font-weight="bold">🤖 AI处理阶段</text>
    <text x="760" y="300" fill="#166534" font-family="Arial, sans-serif" font-size="11">• 知识检索</text>
    <text x="760" y="315" fill="#166534" font-family="Arial, sans-serif" font-size="11">• 模板匹配</text>
    <text x="760" y="330" fill="#166534" font-family="Arial, sans-serif" font-size="11">• 内容生成</text>
    <text x="760" y="345" fill="#166534" font-family="Arial, sans-serif" font-size="11">• 智能组装</text>
    
    <rect x="750" y="380" width="200" height="80" fill="#fefce8" stroke="#fde047" rx="6" filter="url(#shadow)"/>
    <text x="850" y="400" text-anchor="middle" fill="#a16207" font-family="Arial, sans-serif" font-size="14" font-weight="bold">✨ 优化阶段</text>
    <text x="760" y="420" fill="#a16207" font-family="Arial, sans-serif" font-size="11">• 用户反馈</text>
    <text x="760" y="435" fill="#a16207" font-family="Arial, sans-serif" font-size="11">• 内容优化</text>
    <text x="760" y="450" fill="#a16207" font-family="Arial, sans-serif" font-size="11">• 最终输出</text>
  </g>
  
  <!-- Key features -->
  <rect x="50" y="480" width="640" height="120" fill="#f8fafc" stroke="#e2e8f0" rx="6" filter="url(#shadow)"/>
  <text x="370" y="505" text-anchor="middle" fill="#1e293b" font-family="Arial, sans-serif" font-size="16" font-weight="bold">🎯 核心特性说明</text>
  
  <text x="70" y="530" fill="#475569" font-family="Arial, sans-serif" font-size="12" font-weight="bold">🔍 智能检索：</text>
  <text x="160" y="530" fill="#475569" font-family="Arial, sans-serif" font-size="11">AI自动从知识库检索相关法律条款和合规要求</text>
  
  <text x="70" y="550" fill="#475569" font-family="Arial, sans-serif" font-size="12" font-weight="bold">📝 模板匹配：</text>
  <text x="160" y="550" fill="#475569" font-family="Arial, sans-serif" font-size="11">基于用户需求智能匹配最适合的合同模板</text>
  
  <text x="70" y="570" fill="#475569" font-family="Arial, sans-serif" font-size="12" font-weight="bold">🎨 内容生成：</text>
  <text x="160" y="570" fill="#475569" font-family="Arial, sans-serif" font-size="11">结合法律条款和模板结构，生成个性化合同内容</text>
  
  <text x="70" y="590" fill="#475569" font-family="Arial, sans-serif" font-size="12" font-weight="bold">🔧 迭代优化：</text>
  <text x="160" y="590" fill="#475569" font-family="Arial, sans-serif" font-size="11">支持多轮对话优化，确保合同内容符合用户期望</text>
  
  <!-- Footer -->
  <text x="500" y="650" text-anchor="middle" fill="#64748b" font-family="Arial, sans-serif" font-size="11">路浩智能电子签 - AI驱动的智能合同生成系统</text>
  <text x="500" y="670" text-anchor="middle" fill="#94a3b8" font-family="Arial, sans-serif" font-size="10">通过多轮对话和智能分析，将合同起草时间从数小时缩短至几分钟</text>
</svg>