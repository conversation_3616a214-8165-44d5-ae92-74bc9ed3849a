<svg viewBox="0 0 1200 900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151" />
    </marker>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="900" fill="#f8fafc"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1200" height="60" fill="url(#headerGradient)"/>
  <text x="600" y="35" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="bold">用户注册与认证流程图</text>
  <text x="600" y="50" text-anchor="middle" fill="#e2e8f0" font-family="Arial, sans-serif" font-size="12">个人用户完整注册认证流程</text>
  
  <!-- A: 用户访问平台 -->
  <ellipse cx="600" cy="120" rx="80" ry="25" fill="#059669" filter="url(#shadow)"/>
  <text x="600" y="125" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">用户访问平台</text>
  
  <!-- Arrow A to B -->
  <line x1="600" y1="145" x2="600" y2="170" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- B: 选择注册方式 -->
  <rect x="540" y="180" width="120" height="40" fill="#3b82f6" rx="8" filter="url(#shadow)"/>
  <text x="600" y="205" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">选择注册方式</text>
  
  <!-- Arrow B to C -->
  <line x1="600" y1="220" x2="600" y2="245" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- C: 注册方式决策点 -->
  <polygon points="600,250 640,280 600,310 560,280" fill="#f59e0b" filter="url(#shadow)"/>
  <text x="600" y="285" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">注册方式</text>
  
  <!-- Arrow C to D (手机号注册) -->
  <line x1="560" y1="280" x2="400" y2="280" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="480" y="275" text-anchor="middle" fill="#374151" font-family="Arial, sans-serif" font-size="10">手机号注册</text>
  
  <!-- Arrow C to E (微信授权) -->
  <line x1="640" y1="280" x2="800" y2="280" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="720" y="275" text-anchor="middle" fill="#374151" font-family="Arial, sans-serif" font-size="10">微信授权</text>
  
  <!-- D: 输入手机号获取验证码 -->
  <rect x="300" y="260" width="140" height="40" fill="#ef4444" rx="8" filter="url(#shadow)"/>
  <text x="370" y="280" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">输入手机号</text>
  <text x="370" y="290" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">获取验证码</text>
  
  <!-- E: 微信一键授权登录 -->
  <rect x="760" y="260" width="140" height="40" fill="#10b981" rx="8" filter="url(#shadow)"/>
  <text x="830" y="280" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">微信一键</text>
  <text x="830" y="290" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">授权登录</text>
  
  <!-- Arrow D to F -->
  <line x1="370" y1="300" x2="370" y2="340" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Arrow E to F -->
  <line x1="830" y1="300" x2="830" y2="340" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="830" y1="360" x2="600" y2="360" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- F: 验证码验证成功 -->
  <rect x="310" y="350" width="120" height="40" fill="#8b5cf6" rx="8" filter="url(#shadow)"/>
  <text x="370" y="375" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">验证码验证成功</text>
  
  <!-- Arrow F to G -->
  <line x1="430" y1="370" x2="540" y2="370" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- G: 创建用户账号 -->
  <rect x="540" y="350" width="120" height="40" fill="#059669" rx="8" filter="url(#shadow)"/>
  <text x="600" y="375" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">创建用户账号</text>
  
  <!-- Arrow G to H -->
  <line x1="600" y1="390" x2="600" y2="415" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- H: 引导完成实名认证 -->
  <rect x="540" y="425" width="120" height="40" fill="#dc2626" rx="8" filter="url(#shadow)"/>
  <text x="600" y="450" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">引导完成</text>
  <text x="600" y="460" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">实名认证</text>
  
  <!-- Arrow H to I -->
  <line x1="600" y1="465" x2="600" y2="490" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- I: 上传身份证信息 -->
  <rect x="540" y="500" width="120" height="40" fill="#7c3aed" rx="8" filter="url(#shadow)"/>
  <text x="600" y="525" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">上传身份证信息</text>
  
  <!-- Arrow I to J -->
  <line x1="600" y1="540" x2="600" y2="565" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- J: 人脸识别验证 -->
  <rect x="540" y="575" width="120" height="40" fill="#ea580c" rx="8" filter="url(#shadow)"/>
  <text x="600" y="600" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">人脸识别验证</text>
  
  <!-- Arrow J to K -->
  <line x1="600" y1="615" x2="600" y2="640" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- K: 认证结果决策点 -->
  <polygon points="600,645 640,675 600,705 560,675" fill="#f59e0b" filter="url(#shadow)"/>
  <text x="600" y="680" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">认证结果</text>
  
  <!-- Arrow K to L (成功) -->
  <line x1="640" y1="675" x2="780" y2="675" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="710" y="670" text-anchor="middle" fill="#374151" font-family="Arial, sans-serif" font-size="10">成功</text>
  
  <!-- Arrow K to M (失败) -->
  <line x1="560" y1="675" x2="420" y2="675" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="490" y="670" text-anchor="middle" fill="#374151" font-family="Arial, sans-serif" font-size="10">失败</text>
  
  <!-- L: 实名认证完成 -->
  <rect x="780" y="655" width="120" height="40" fill="#059669" rx="8" filter="url(#shadow)"/>
  <text x="840" y="680" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">实名认证完成</text>
  
  <!-- M: 提示重新认证 -->
  <rect x="320" y="655" width="120" height="40" fill="#ef4444" rx="8" filter="url(#shadow)"/>
  <text x="380" y="680" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">提示重新认证</text>
  
  <!-- Arrow M to I (回到上传身份证) -->
  <line x1="380" y1="655" x2="380" y2="520" stroke="#374151" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="380" y1="520" x2="540" y2="520" stroke="#374151" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
  
  <!-- Arrow L to N -->
  <line x1="840" y1="695" x2="840" y2="720" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- N: 创建个人签名 -->
  <rect x="780" y="730" width="120" height="40" fill="#6366f1" rx="8" filter="url(#shadow)"/>
  <text x="840" y="755" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">创建个人签名</text>
  
  <!-- Arrow N to O -->
  <line x1="840" y1="770" x2="840" y2="795" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- O: 开始使用平台功能 -->
  <ellipse cx="840" cy="820" rx="80" ry="25" fill="#10b981" filter="url(#shadow)"/>
  <text x="840" y="825" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">开始使用平台功能</text>
  
  <!-- Side information panel -->
  <rect x="50" y="120" width="200" height="300" fill="#f1f5f9" stroke="#cbd5e1" rx="8" filter="url(#shadow)"/>
  <text x="150" y="145" text-anchor="middle" fill="#1e293b" font-family="Arial, sans-serif" font-size="14" font-weight="bold">🔐 认证特点</text>
  
  <text x="65" y="170" fill="#475569" font-family="Arial, sans-serif" font-size="11" font-weight="bold">📱 多种注册方式：</text>
  <text x="65" y="185" fill="#64748b" font-family="Arial, sans-serif" font-size="10">• 手机号注册</text>
  <text x="65" y="200" fill="#64748b" font-family="Arial, sans-serif" font-size="10">• 微信一键授权</text>
  
  <text x="65" y="225" fill="#475569" font-family="Arial, sans-serif" font-size="11" font-weight="bold">🛡️ 安全认证：</text>
  <text x="65" y="240" fill="#64748b" font-family="Arial, sans-serif" font-size="10">• 身份证OCR识别</text>
  <text x="65" y="255" fill="#64748b" font-family="Arial, sans-serif" font-size="10">• 人脸识别验证</text>
  <text x="65" y="270" fill="#64748b" font-family="Arial, sans-serif" font-size="10">• 活体检测防护</text>
  
  <text x="65" y="295" fill="#475569" font-family="Arial, sans-serif" font-size="11" font-weight="bold">✨ 用户体验：</text>
  <text x="65" y="310" fill="#64748b" font-family="Arial, sans-serif" font-size="10">• 流程简化</text>
  <text x="65" y="325" fill="#64748b" font-family="Arial, sans-serif" font-size="10">• 智能引导</text>
  <text x="65" y="340" fill="#64748b" font-family="Arial, sans-serif" font-size="10">• 异常处理</text>
  <text x="65" y="355" fill="#64748b" font-family="Arial, sans-serif" font-size="10">• 重试机制</text>
  
  <text x="65" y="380" fill="#475569" font-family="Arial, sans-serif" font-size="11" font-weight="bold">⚡ 效率提升：</text>
  <text x="65" y="395" fill="#64748b" font-family="Arial, sans-serif" font-size="10">• 3分钟快速注册</text>
  <text x="65" y="410" fill="#64748b" font-family="Arial, sans-serif" font-size="10">• 自动化认证</text>
  
  <!-- Status indicators -->
  <g id="status-legend">
    <rect x="950" y="120" width="200" height="200" fill="#f8fafc" stroke="#e2e8f0" rx="8" filter="url(#shadow)"/>
    <text x="1050" y="145" text-anchor="middle" fill="#1e293b" font-family="Arial, sans-serif" font-size="14" font-weight="bold">📊 流程状态</text>
    
    <circle cx="970" cy="170" r="8" fill="#059669"/>
    <text x="990" y="175" fill="#374151" font-family="Arial, sans-serif" font-size="10">开始/结束节点</text>
    
    <rect x="962" y="185" width="16" height="16" fill="#3b82f6" rx="3"/>
    <text x="990" y="195" fill="#374151" font-family="Arial, sans-serif" font-size="10">处理节点</text>
    
    <polygon points="970,205 978,213 970,221 962,213" fill="#f59e0b"/>
    <text x="990" y="215" fill="#374151" font-family="Arial, sans-serif" font-size="10">决策节点</text>
    
    <line x1="962" y1="235" x2="978" y2="235" stroke="#374151" stroke-width="2" stroke-dasharray="3,3"/>
    <text x="990" y="240" fill="#374151" font-family="Arial, sans-serif" font-size="10">重试路径</text>
    
    <rect x="962" y="250" width="16" height="16" fill="#ef4444" rx="3"/>
    <text x="990" y="260" fill="#374151" font-family="Arial, sans-serif" font-size="10">异常处理</text>
    
    <rect x="962" y="275" width="16" height="16" fill="#10b981" rx="3"/>
    <text x="990" y="285" fill="#374151" font-family="Arial, sans-serif" font-size="10">成功完成</text>
  </g>
  
  <!-- Footer info -->
  <text x="600" y="870" text-anchor="middle" fill="#64748b" font-family="Arial, sans-serif" font-size="11">路浩智能电子签 - 用户注册与实名认证流程</text>
  <text x="600" y="885" text-anchor="middle" fill="#94a3b8" font-family="Arial, sans-serif" font-size="10">支持多种注册方式，3分钟完成账号创建和实名认证</text>
</svg>