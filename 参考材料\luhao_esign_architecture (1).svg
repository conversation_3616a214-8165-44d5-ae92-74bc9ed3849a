<svg viewBox="0 0 1400 1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6"/>
      <stop offset="100%" style="stop-color:#1D4ED8"/>
    </linearGradient>
    <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981"/>
      <stop offset="100%" style="stop-color:#047857"/>
    </linearGradient>
    <linearGradient id="orangeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F59E0B"/>
      <stop offset="100%" style="stop-color:#D97706"/>
    </linearGradient>
    <linearGradient id="purpleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6"/>
      <stop offset="100%" style="stop-color:#7C3AED"/>
    </linearGradient>
    <linearGradient id="tealGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#14B8A6"/>
      <stop offset="100%" style="stop-color:#0F766E"/>
    </linearGradient>
    <linearGradient id="indigoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366F1"/>
      <stop offset="100%" style="stop-color:#4338CA"/>
    </linearGradient>
    <linearGradient id="redGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#EF4444"/>
      <stop offset="100%" style="stop-color:#DC2626"/>
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.3)"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1400" height="1000" fill="#F8FAFC"/>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#1E293B">路浩智能电子签系统架构图</text>
  
  <!-- 用户接入层 -->
  <g>
    <rect x="50" y="60" width="1300" height="80" fill="url(#blueGradient)" rx="8" filter="url(#shadow)"/>
    <text x="700" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">用户接入层</text>
    
    <rect x="350" y="105" width="150" height="25" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="425" y="122" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">PC Web管理端</text>
    <text x="425" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">React+Next.js</text>
    
    <rect x="530" y="105" width="150" height="25" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="605" y="122" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">H5移动签署端</text>
    <text x="605" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">React+Vite</text>
    
    <rect x="710" y="105" width="150" height="25" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="785" y="122" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">微信小程序</text>
    <text x="785" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">Taro</text>
  </g>
  
  <!-- API网关层 -->
  <g>
    <rect x="50" y="160" width="1300" height="50" fill="#0F172A" rx="8" filter="url(#shadow)"/>
    <text x="700" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">API网关层</text>
    <text x="700" y="195" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#CBD5E1">Spring Cloud Gateway - 路由+限流+认证+熔断</text>
    <text x="700" y="208" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#CBD5E1">Nginx + API Gateway</text>
  </g>
  
  <!-- 微服务层 -->
  <g>
    <!-- Java微服务 -->
    <rect x="50" y="230" width="650" height="200" fill="url(#greenGradient)" rx="8" filter="url(#shadow)"/>
    <text x="375" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">Java微服务层</text>
    
    <!-- 第一行服务 -->
    <rect x="80" y="270" width="120" height="50" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="140" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">用户服务</text>
    <text x="140" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">user-service</text>
    
    <rect x="220" y="270" width="120" height="50" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="280" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">企业服务</text>
    <text x="280" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">enterprise-service</text>
    
    <rect x="360" y="270" width="120" height="50" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="420" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">组织权限服务</text>
    <text x="420" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">org-service</text>
    
    <rect x="500" y="270" width="120" height="50" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="560" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">合同服务</text>
    <text x="560" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">contract-service</text>
    
    <!-- 第二行服务 -->
    <rect x="80" y="340" width="120" height="50" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="140" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">印章服务</text>
    <text x="140" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">seal-service</text>
    
    <rect x="220" y="340" width="120" height="50" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="280" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">计费服务</text>
    <text x="280" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">billing-service</text>
    
    <rect x="360" y="340" width="120" height="50" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="420" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">审批流服务</text>
    <text x="420" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">workflow-service</text>
    
    <rect x="500" y="340" width="120" height="50" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="560" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">模板服务</text>
    <text x="560" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">template-service</text>
    
    <!-- Go微服务 -->
    <rect x="720" y="230" width="630" height="200" fill="url(#orangeGradient)" rx="8" filter="url(#shadow)"/>
    <text x="1035" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">Go微服务层</text>
    
    <!-- 第一行服务 -->
    <rect x="750" y="270" width="120" height="50" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="810" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">AI服务</text>
    <text x="810" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">ai-service</text>
    
    <rect x="890" y="270" width="120" height="50" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="950" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">文件服务</text>
    <text x="950" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">file-service</text>
    
    <rect x="1030" y="270" width="120" height="50" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="1090" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">消息推送服务</text>
    <text x="1090" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">notification-service</text>
    
    <rect x="1170" y="270" width="120" height="50" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="1230" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">存证服务</text>
    <text x="1230" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">evidence-service</text>
    
    <!-- 第二行服务 -->
    <rect x="750" y="340" width="120" height="50" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="810" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">OCR服务</text>
    <text x="810" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">ocr-service</text>
    
    <rect x="890" y="340" width="120" height="50" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="950" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">运营系统</text>
    <text x="950" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">admin-service</text>
    
    <rect x="1030" y="340" width="120" height="50" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="1090" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">大数据服务</text>
    <text x="1090" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">bigdata-service</text>
    
    <rect x="1170" y="340" width="120" height="50" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="1230" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">网关服务</text>
    <text x="1230" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">gateway-service</text>
  </g>
  
  <!-- 中间件层 -->
  <g>
    <rect x="50" y="450" width="1300" height="80" fill="url(#purpleGradient)" rx="8" filter="url(#shadow)"/>
    <text x="700" y="470" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">中间件层</text>
    
    <rect x="100" y="485" width="150" height="35" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="175" y="500" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">RabbitMQ</text>
    <text x="175" y="515" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">业务消息队列</text>
    
    <rect x="280" y="485" width="150" height="35" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="355" y="500" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Kafka</text>
    <text x="355" y="515" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">日志消息流</text>
    
    <rect x="460" y="485" width="150" height="35" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="535" y="500" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Nacos</text>
    <text x="535" y="515" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">服务注册发现</text>
    
    <rect x="640" y="485" width="150" height="35" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="715" y="500" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">XXL-Job</text>
    <text x="715" y="515" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">任务调度</text>
    
    <rect x="820" y="485" width="150" height="35" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="895" y="500" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">ElasticSearch</text>
    <text x="895" y="515" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">全文搜索</text>
    
    <rect x="1000" y="485" width="150" height="35" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="1075" y="500" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Zipkin</text>
    <text x="1075" y="515" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">链路追踪</text>
  </g>
  
  <!-- 数据存储层 -->
  <g>
    <rect x="50" y="550" width="1300" height="120" fill="#1E293B" rx="8" filter="url(#shadow)"/>
    <text x="700" y="575" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">数据存储层</text>
    
    <!-- 第一行存储 -->
    <rect x="100" y="590" width="180" height="30" fill="rgba(255,255,255,0.15)" rx="4"/>
    <text x="190" y="610" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">PostgreSQL</text>
    <text x="190" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#CBD5E1">主业务数据库</text>
    
    <rect x="300" y="590" width="180" height="30" fill="rgba(255,255,255,0.15)" rx="4"/>
    <text x="390" y="610" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">MongoDB</text>
    <text x="390" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#CBD5E1">日志&文档数据</text>
    
    <rect x="500" y="590" width="180" height="30" fill="rgba(255,255,255,0.15)" rx="4"/>
    <text x="590" y="610" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Redis</text>
    <text x="590" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#CBD5E1">缓存&会话</text>
    
    <rect x="700" y="590" width="180" height="30" fill="rgba(255,255,255,0.15)" rx="4"/>
    <text x="790" y="610" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">ClickHouse</text>
    <text x="790" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#CBD5E1">数据分析</text>
    
    <rect x="900" y="590" width="180" height="30" fill="rgba(255,255,255,0.15)" rx="4"/>
    <text x="990" y="610" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">MinIO</text>
    <text x="990" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#CBD5E1">对象存储</text>
    
    <rect x="1100" y="590" width="180" height="30" fill="rgba(255,255,255,0.15)" rx="4"/>
    <text x="1190" y="610" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Milvus</text>
    <text x="1190" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#CBD5E1">向量数据库</text>
  </g>
  
  <!-- 监控运维层 -->
  <g>
    <rect x="50" y="690" width="650" height="80" fill="url(#indigoGradient)" rx="8" filter="url(#shadow)"/>
    <text x="375" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">监控运维层</text>
    
    <rect x="100" y="735" width="140" height="25" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="170" y="750" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">Prometheus</text>
    <text x="170" y="762" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">指标监控</text>
    
    <rect x="260" y="735" width="140" height="25" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="330" y="750" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">Grafana</text>
    <text x="330" y="762" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">可视化</text>
    
    <rect x="420" y="735" width="140" height="25" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="490" y="750" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">ELK Stack</text>
    <text x="490" y="762" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">日志分析</text>
    
    <rect x="580" y="735" width="100" height="25" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="630" y="750" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">Zabbix</text>
    <text x="630" y="762" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">告警</text>
  </g>
  
  <!-- 基础设施层 -->
  <g>
    <rect x="720" y="690" width="630" height="80" fill="url(#tealGradient)" rx="8" filter="url(#shadow)"/>
    <text x="1035" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">基础设施层</text>
    
    <rect x="760" y="735" width="120" height="25" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="820" y="750" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">Kubernetes</text>
    <text x="820" y="762" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">容器编排</text>
    
    <rect x="900" y="735" width="120" height="25" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="960" y="750" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">Docker</text>
    <text x="960" y="762" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">容器化</text>
    
    <rect x="1040" y="735" width="120" height="25" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="1100" y="750" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">SLB</text>
    <text x="1100" y="762" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">负载均衡</text>
    
    <rect x="1180" y="735" width="140" height="25" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="1250" y="750" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">CI/CD Pipeline</text>
    <text x="1250" y="762" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">自动化部署</text>
  </g>
  
  <!-- 第三方服务层 -->
  <g>
    <rect x="50" y="790" width="1300" height="80" fill="url(#redGradient)" rx="8" filter="url(#shadow)"/>
    <text x="700" y="815" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">第三方服务层</text>
    
    <rect x="100" y="835" width="160" height="25" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="180" y="850" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">CA数字证书</text>
    <text x="180" y="862" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">数字签名认证</text>
    
    <rect x="280" y="835" width="160" height="25" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="360" y="850" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">时间戳服务</text>
    <text x="360" y="862" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">权威时间认证</text>
    
    <rect x="460" y="835" width="200" height="25" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="560" y="850" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">哈希链&数字签名存证</text>
    <text x="560" y="862" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">不可篡改存证</text>
    
    <rect x="680" y="835" width="160" height="25" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="760" y="850" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">短信邮件</text>
    <text x="760" y="862" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">通知服务</text>
    
    <rect x="860" y="835" width="160" height="25" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="940" y="850" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">大模型API</text>
    <text x="940" y="862" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">AI能力支撑</text>
    
    <rect x="1040" y="835" width="160" height="25" fill="rgba(255,255,255,0.2)" rx="4"/>
    <text x="1120" y="850" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">支付网关</text>
    <text x="1120" y="862" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="rgba(255,255,255,0.8)">在线支付</text>
  </g>
  